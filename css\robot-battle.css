/* Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Courier New', monospace;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #0A0A1A;
    color: #FFFFFF;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    overflow: hidden;
}

.game-container {
    width: 100%;
    max-width: 1200px;
    aspect-ratio: 16/9;
    background-color: #121212;
    border-radius: 10px;
    box-shadow: 0 0 20px rgba(0, 240, 255, 0.3);
    display: flex;
    flex-direction: column;
    padding: 20px;
}

/* Battle Arena */
.battle-arena {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 60%;
    padding: 0 20px;
}

.player-side, .enemy-side {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 45%;
    height: 100%;
}

.vs-indicator {
    font-size: 3rem;
    color: #00F0FF;
    text-shadow: 0 0 10px #00F0FF;
    animation: pulse 2s infinite;
}

/* Robot Styling */
.robot {
    width: 150px;
    height: 200px;
    position: relative;
    margin-bottom: 20px;
}

.robot-sprite {
    width: 100%;
    height: 100%;
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

/* Player Robot - Simple Cute Blue Robot */
.player-robot .robot-sprite {
    animation: idle-bounce 3s ease-in-out infinite;
    position: relative;
}

/* Simplified main body - one large circle */
.player-robot .robot-sprite .robot-head {
    position: absolute;
    top: 30px;
    width: 120px;
    height: 120px;
    background: linear-gradient(135deg, #00F0FF, #0088FF);
    border-radius: 50%;
    border: 3px solid #FFFFFF;
    box-shadow: 0 0 15px rgba(0, 240, 255, 0.5);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
}

.player-robot .robot-sprite .robot-eyes {
    display: flex;
    gap: 25px;
    margin-bottom: 8px;
}

.player-robot .robot-sprite .robot-eye {
    width: 18px;
    height: 18px;
    background: #000;
    border-radius: 50%;
    animation: blink 4s infinite;
}

.player-robot .robot-sprite .robot-mouth {
    width: 30px;
    height: 15px;
    border: 3px solid #000;
    border-top: none;
    border-radius: 0 0 30px 30px;
    background: transparent;
}

.player-robot .robot-sprite .robot-cheeks {
    position: absolute;
    top: 55px;
    width: 100%;
    height: 20px;
    display: flex;
    justify-content: space-between;
    padding: 0 15px;
}

.player-robot .robot-sprite .robot-cheek {
    width: 15px;
    height: 15px;
    background: #FFB6C1;
    border-radius: 50%;
    animation: cheek-glow 4s ease-in-out infinite;
}

.player-robot .robot-sprite .robot-antenna {
    position: absolute;
    top: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 4px;
    height: 25px;
    background: #FFFFFF;
    border-radius: 2px;
}

.player-robot .robot-sprite .robot-antenna::after {
    content: '';
    position: absolute;
    top: -10px;
    left: -6px;
    width: 16px;
    height: 16px;
    background: #00FF00;
    border-radius: 50%;
    animation: antenna-blink 2s infinite;
}

/* Simple circular body */
.player-robot .robot-sprite .robot-body {
    position: absolute;
    top: 160px;
    width: 80px;
    height: 40px;
    background: #2A2A3A;
    border-radius: 40px;
    border: 2px solid #00F0FF;
    display: flex;
    align-items: center;
    justify-content: center;
    left: 20px;
}

.player-robot .robot-sprite .robot-panel {
    width: 12px;
    height: 12px;
    background: #00F0FF;
    border-radius: 50%;
    animation: panel-glow 3s ease-in-out infinite;
}

/* Hide complex parts for simplicity */
.player-robot .robot-sprite .robot-arms {
    display: none;
}

.player-robot .robot-sprite .robot-legs {
    display: none;
}

/* Enemy Robot - Simple Cute Red Robot */
.enemy-robot .robot-sprite {
    animation: idle-bounce 3.5s ease-in-out infinite;
    transform: scaleX(-1);
    position: relative;
}

/* Simplified main body - one large circle */
.enemy-robot .robot-sprite .robot-head {
    position: absolute;
    top: 30px;
    width: 120px;
    height: 120px;
    background: linear-gradient(135deg, #FF0055, #FF5500);
    border-radius: 50%;
    border: 3px solid #FFFFFF;
    box-shadow: 0 0 15px rgba(255, 0, 85, 0.5);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
}

.enemy-robot .robot-sprite .robot-eyes {
    display: flex;
    gap: 25px;
    margin-bottom: 8px;
}

.enemy-robot .robot-sprite .robot-eye {
    width: 18px;
    height: 18px;
    background: #000;
    border-radius: 50%;
    animation: blink 3.5s infinite;
}

.enemy-robot .robot-sprite .robot-mouth {
    width: 30px;
    height: 15px;
    border: 3px solid #000;
    border-top: none;
    border-radius: 0 0 30px 30px;
    background: transparent;
}

.enemy-robot .robot-sprite .robot-cheeks {
    position: absolute;
    top: 55px;
    width: 100%;
    height: 20px;
    display: flex;
    justify-content: space-between;
    padding: 0 15px;
}

.enemy-robot .robot-sprite .robot-cheek {
    width: 15px;
    height: 15px;
    background: #FFB6C1;
    border-radius: 50%;
    animation: cheek-glow 3.5s ease-in-out infinite;
}

.enemy-robot .robot-sprite .robot-antenna {
    position: absolute;
    top: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 4px;
    height: 25px;
    background: #FFFFFF;
    border-radius: 2px;
}

.enemy-robot .robot-sprite .robot-antenna::after {
    content: '';
    position: absolute;
    top: -10px;
    left: -6px;
    width: 16px;
    height: 16px;
    background: #FF0000;
    border-radius: 50%;
    animation: antenna-blink 1.5s infinite;
}

/* Simple circular body */
.enemy-robot .robot-sprite .robot-body {
    position: absolute;
    top: 160px;
    width: 80px;
    height: 40px;
    background: #3A2A2A;
    border-radius: 40px;
    border: 2px solid #FF0055;
    display: flex;
    align-items: center;
    justify-content: center;
    left: 20px;
}

.enemy-robot .robot-sprite .robot-panel {
    width: 12px;
    height: 12px;
    background: #FF0055;
    border-radius: 50%;
    animation: panel-glow 2.5s ease-in-out infinite;
}

/* Hide complex parts for simplicity */
.enemy-robot .robot-sprite .robot-arms {
    display: none;
}

.enemy-robot .robot-sprite .robot-legs {
    display: none;
}

/* Health Bars */
.health-container {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.health-label {
    font-size: 1.2rem;
    margin-bottom: 5px;
    color: #00F0FF;
}

.health-bar-container {
    width: 100%;
    height: 20px;
    background-color: #333;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 0 5px rgba(0, 240, 255, 0.5);
}

.health-bar {
    height: 100%;
    border-radius: 10px;
    transition: width 0.3s ease;
}

.player-health {
    width: 100%;
    background: linear-gradient(90deg, #00F0FF, #0088FF);
}

.enemy-health {
    width: 100%;
    background: linear-gradient(90deg, #FF0055, #FF5500);
}

.health-value {
    font-size: 0.9rem;
    margin-top: 5px;
    color: #FFFFFF;
}

/* Question Area */
.question-area {
    height: 40%;
    background-color: rgba(0, 0, 0, 0.3);
    border-radius: 10px;
    padding: 15px;
    display: flex;
    flex-direction: column;
    margin-top: 20px;
}

.timer-container {
    display: flex;
    justify-content: center;
    margin-bottom: 15px;
}

.timer {
    width: 80%;
    height: 30px;
    background-color: #333;
    border-radius: 15px;
    overflow: hidden;
    position: relative;
}

.timer-bar {
    height: 100%;
    width: 100%;
    background: linear-gradient(90deg, #00F0FF, #0088FF);
    border-radius: 15px;
    transition: width 0.1s linear;
}

.timer-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: #FFFFFF;
    font-weight: bold;
}

.question-container {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
}

.question-text {
    text-align: center;
    margin-bottom: 20px;
    color: #FFFFFF;
}

.options-container {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    grid-gap: 15px;
    padding: 0 10px;
}

.option-btn {
    background-color: #1A1A2A;
    color: #FFFFFF;
    border: 2px solid #00F0FF;
    border-radius: 5px;
    padding: 10px;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.2s;
    box-shadow: 0 0 5px rgba(0, 240, 255, 0.3);
}

.option-btn:hover {
    background-color: #2A2A3A;
    box-shadow: 0 0 10px rgba(0, 240, 255, 0.5);
}

.option-btn:active {
    transform: scale(0.98);
}

/* Modals */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    backdrop-filter: blur(5px);
}

.modal-content {
    background: linear-gradient(145deg, #1a1a3a, #2a2a4a);
    border-radius: 20px;
    padding: 40px;
    max-width: 600px;
    width: 90%;
    text-align: center;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5), 0 0 30px rgba(0, 240, 255, 0.3);
    border: 2px solid #00F0FF;
    position: relative;
    overflow: hidden;
}

.modal-content::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #00F0FF, #0088FF, #00F0FF);
    animation: shimmer 2s infinite;
}

.modal h2 {
    color: #4fc3f7;
    margin-bottom: 25px;
    font-size: 2.5rem;
    text-shadow: 0 0 10px rgba(79, 195, 247, 0.5);
    font-weight: normal;
    text-transform: none;
    letter-spacing: normal;
}

#instruction-title {
    color: #4fc3f7;
    font-size: 2.5rem;
    margin-bottom: 30px;
    text-shadow: 0 0 10px rgba(79, 195, 247, 0.5);
}

#result-title {
    color: #4fc3f7;
    font-size: 2.5rem;
    margin-bottom: 20px;
    text-shadow: 0 0 10px rgba(79, 195, 247, 0.5);
}

.instructions {
    background: rgba(79, 195, 247, 0.1);
    padding: 25px;
    border-radius: 15px;
    border: 1px solid rgba(79, 195, 247, 0.2);
    margin-bottom: 30px;
    text-align: left;
}

.instructions p {
    color: #fff;
    font-size: 1.2rem;
    margin-bottom: 20px;
    line-height: 1.6;
    text-align: center;
}

.instructions ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.instructions li {
    color: #fff;
    font-size: 1.1rem;
    margin-bottom: 15px;
    padding-left: 30px;
    position: relative;
    line-height: 1.5;
}

.instructions li::before {
    content: '⚡';
    position: absolute;
    left: 0;
    color: #ffeb3b;
    font-size: 1.2rem;
    animation: sparkle 1.5s infinite;
}

/* Enhanced Stars */
.stars {
    font-size: 4rem;
    margin: 30px 0;
    letter-spacing: 15px;
    text-align: center;
    filter: drop-shadow(0 0 10px rgba(255, 235, 59, 0.8));
    animation: starGlow 2s ease-in-out infinite alternate;
}

.stars span {
    display: inline-block;
    transition: all 0.3s ease;
    animation: starPulse 1.5s ease-in-out infinite;
}

.stars span:nth-child(1) {
    animation-delay: 0s;
}

.stars span:nth-child(2) {
    animation-delay: 0.2s;
}

.stars span:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes starGlow {
    0% { filter: drop-shadow(0 0 10px rgba(255, 235, 59, 0.8)); }
    100% { filter: drop-shadow(0 0 20px rgba(255, 235, 59, 1)); }
}

@keyframes starPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

#result-message {
    color: #fff;
    font-size: 1.2rem;
    margin: 20px 0;
    line-height: 1.5;
}

/* Enhanced EXP Container */
.exp-container {
    background: linear-gradient(145deg, #2a2a4a, #1a1a3a);
    border-radius: 20px;
    padding: 25px 40px;
    margin: 30px 0;
    border: 3px solid #4fc3f7;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 20px;
    box-shadow:
        0 0 30px rgba(79, 195, 247, 0.4),
        inset 0 0 20px rgba(79, 195, 247, 0.1);
    position: relative;
    overflow: hidden;
    animation: expGlow 3s ease-in-out infinite alternate;
}

.exp-container::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(79, 195, 247, 0.1), transparent);
    animation: expShine 2s linear infinite;
}

.exp-icon {
    font-size: 3.5rem;
    animation: sparkleEnhanced 2s infinite;
    filter: drop-shadow(0 0 15px rgba(255, 235, 59, 0.8));
    z-index: 1;
}

.exp-text {
    font-size: 2.5rem;
    font-weight: bold;
    color: #4fc3f7;
    text-shadow:
        0 0 15px rgba(79, 195, 247, 0.8),
        0 0 30px rgba(79, 195, 247, 0.4);
    z-index: 1;
    animation: expTextPulse 2s ease-in-out infinite;
}

@keyframes expGlow {
    0% {
        box-shadow:
            0 0 30px rgba(79, 195, 247, 0.4),
            inset 0 0 20px rgba(79, 195, 247, 0.1);
    }
    100% {
        box-shadow:
            0 0 50px rgba(79, 195, 247, 0.8),
            inset 0 0 30px rgba(79, 195, 247, 0.2);
    }
}

@keyframes expShine {
    0% { transform: translateX(-100%) translateY(-100%); }
    100% { transform: translateX(100%) translateY(100%); }
}

@keyframes sparkleEnhanced {
    0%, 100% {
        transform: scale(1);
        filter: drop-shadow(0 0 15px rgba(255, 235, 59, 0.8));
    }
    25% {
        transform: scale(1.2);
        filter: drop-shadow(0 0 25px rgba(255, 235, 59, 1));
    }
    50% {
        transform: scale(1.1);
        filter: drop-shadow(0 0 20px rgba(255, 235, 59, 0.9));
    }
    75% {
        transform: scale(1.3);
        filter: drop-shadow(0 0 30px rgba(255, 235, 59, 1));
    }
}

@keyframes expTextPulse {
    0%, 100% {
        transform: scale(1);
        text-shadow:
            0 0 15px rgba(79, 195, 247, 0.8),
            0 0 30px rgba(79, 195, 247, 0.4);
    }
    50% {
        transform: scale(1.05);
        text-shadow:
            0 0 25px rgba(79, 195, 247, 1),
            0 0 50px rgba(79, 195, 247, 0.6);
    }
}

/* Special star entrance animation */
.stars.animate-in span {
    animation: starEntrance 0.8s ease-out forwards;
    opacity: 0;
    transform: scale(0);
}

.stars.animate-in span:nth-child(1) {
    animation-delay: 0.2s;
}

.stars.animate-in span:nth-child(2) {
    animation-delay: 0.4s;
}

.stars.animate-in span:nth-child(3) {
    animation-delay: 0.6s;
}

@keyframes starEntrance {
    0% {
        opacity: 0;
        transform: scale(0);
    }
    50% {
        opacity: 1;
        transform: scale(1.3);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

/* EXP entrance animation */
.exp-container.animate-in {
    animation: expEntrance 1s ease-out forwards;
    opacity: 0;
    transform: translateY(30px) scale(0.8);
}

@keyframes expEntrance {
    0% {
        opacity: 0;
        transform: translateY(30px) scale(0.8);
    }
    60% {
        opacity: 1;
        transform: translateY(-10px) scale(1.1);
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* Perfect score celebration */
.stars.perfect-score {
    animation: perfectCelebration 2s ease-in-out infinite;
}

.stars.perfect-score span {
    color: #ffeb3b !important;
    animation: perfectStarDance 1.5s ease-in-out infinite;
}

.exp-container.perfect-score {
    border-color: #ffeb3b;
    box-shadow:
        0 0 40px rgba(255, 235, 59, 0.6),
        inset 0 0 30px rgba(255, 235, 59, 0.2);
    animation: perfectExpGlow 2s ease-in-out infinite alternate;
}

.exp-container.perfect-score .exp-text {
    color: #ffeb3b;
    text-shadow:
        0 0 20px rgba(255, 235, 59, 1),
        0 0 40px rgba(255, 235, 59, 0.6);
}

@keyframes perfectCelebration {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

@keyframes perfectStarDance {
    0%, 100% { transform: scale(1); }
    33% { transform: scale(1.2); }
    66% { transform: scale(1.1); }
}

@keyframes perfectExpGlow {
    0% {
        box-shadow:
            0 0 40px rgba(255, 235, 59, 0.6),
            inset 0 0 30px rgba(255, 235, 59, 0.2);
    }
    100% {
        box-shadow:
            0 0 60px rgba(255, 235, 59, 1),
            inset 0 0 50px rgba(255, 235, 59, 0.4);
    }
}

/* Modal Buttons */
.modal-buttons {
    display: flex;
    gap: 20px;
    justify-content: center;
    flex-wrap: wrap;
    margin-top: 30px;
}

.start-btn, .replay-btn, .continue-btn, .try-again-btn, .exit-btn, .main-menu-btn {
    padding: 15px 30px;
    border: none;
    border-radius: 12px;
    font-size: 1.2rem;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 150px;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.start-btn, .replay-btn, .continue-btn {
    background: linear-gradient(145deg, #4fc3f7, #3fb3e7);
    color: #0b0b23;
    box-shadow: 0 4px 0 #2fa3d7;
}

.start-btn:hover, .replay-btn:hover, .continue-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 0 #2fa3d7;
}

.main-menu-btn {
    background: linear-gradient(145deg, #9c27b0, #7b1fa2);
    color: #fff;
    box-shadow: 0 4px 0 #4a148c;
}

.main-menu-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 0 #4a148c;
}

.try-again-btn {
    background: linear-gradient(145deg, #ffeb3b, #ffd600);
    color: #0b0b23;
    box-shadow: 0 4px 0 #ffc107;
}

.try-again-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 0 #ffc107;
}

.exit-btn {
    background: linear-gradient(145deg, #f44336, #d32f2f);
    color: #fff;
    box-shadow: 0 4px 0 #b71c1c;
}

.exit-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 0 #b71c1c;
}

/* Specific start button styling */
#start-btn {
    background: linear-gradient(145deg, #4fc3f7, #3fb3e7);
    color: #0b0b23;
    padding: 15px 40px;
    border: none;
    border-radius: 12px;
    font-size: 1.2rem;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 200px;
    text-transform: uppercase;
    letter-spacing: 1px;
    box-shadow: 0 4px 0 #2fa3d7;
    margin-top: 20px;
}

#start-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 0 #2fa3d7;
}

#start-btn:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(79, 195, 247, 0.3);
}
    transform: scale(1.05);
}

/* Animations */
@keyframes pulse {
    0% { opacity: 0.7; }
    50% { opacity: 1; }
    100% { opacity: 0.7; }
}

@keyframes idle-bounce {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-5px); }
}

@keyframes blink {
    0%, 90%, 100% { transform: scaleY(1); }
    95% { transform: scaleY(0.1); }
}

@keyframes antenna-blink {
    0%, 50% { opacity: 1; box-shadow: 0 0 5px currentColor; }
    25%, 75% { opacity: 0.3; box-shadow: none; }
}

@keyframes panel-glow {
    0%, 100% { opacity: 0.7; }
    50% { opacity: 1; box-shadow: 0 0 10px currentColor; }
}

@keyframes cheek-glow {
    0%, 100% { opacity: 0.6; }
    50% { opacity: 0.9; transform: scale(1.1); }
}

@keyframes arm-float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-3px) rotate(5deg); }
}

.damage-effect {
    position: absolute;
    top: -20px;
    left: -20px;
    width: calc(100% + 40px);
    height: calc(100% + 40px);
    background: radial-gradient(circle, rgba(255, 50, 50, 0.9) 0%, rgba(255, 100, 100, 0.6) 30%, rgba(255, 0, 0, 0) 70%);
    opacity: 0;
    pointer-events: none;
    border-radius: 50%;
    animation: damage-pulse 0.6s ease-out;
}

.attack-animation {
    animation: attack 0.8s ease-in-out;
}

@keyframes attack {
    0% {
        transform: translateX(0) translateY(0) scale(1) rotate(0deg);
        filter: brightness(1) drop-shadow(0 0 5px rgba(0, 240, 255, 0.3));
    }
    15% {
        transform: translateX(-10px) translateY(-5px) scale(1.1) rotate(-15deg);
        filter: brightness(1.3) drop-shadow(0 0 15px rgba(0, 240, 255, 0.8));
    }
    40% {
        transform: translateX(40px) translateY(-25px) scale(1.4) rotate(25deg);
        filter: brightness(1.5) drop-shadow(0 0 25px rgba(0, 240, 255, 1));
    }
    60% {
        transform: translateX(20px) translateY(-15px) scale(1.2) rotate(10deg);
        filter: brightness(1.2) drop-shadow(0 0 20px rgba(0, 240, 255, 0.7));
    }
    80% {
        transform: translateX(-5px) translateY(-5px) scale(1.05) rotate(-5deg);
        filter: brightness(1.1) drop-shadow(0 0 10px rgba(0, 240, 255, 0.5));
    }
    100% {
        transform: translateX(0) translateY(0) scale(1) rotate(0deg);
        filter: brightness(1) drop-shadow(0 0 5px rgba(0, 240, 255, 0.3));
    }
}

.enemy-attack-animation {
    animation: enemy-attack 0.8s ease-in-out;
}

@keyframes enemy-attack {
    0% {
        transform: translateX(0) translateY(0) scaleX(-1) scale(1) rotate(0deg);
        filter: brightness(1) drop-shadow(0 0 5px rgba(255, 0, 85, 0.3));
    }
    15% {
        transform: translateX(10px) translateY(-5px) scaleX(-1) scale(1.1) rotate(15deg);
        filter: brightness(1.3) drop-shadow(0 0 15px rgba(255, 0, 85, 0.8));
    }
    40% {
        transform: translateX(-40px) translateY(-25px) scaleX(-1) scale(1.4) rotate(-25deg);
        filter: brightness(1.5) drop-shadow(0 0 25px rgba(255, 0, 85, 1));
    }
    60% {
        transform: translateX(-20px) translateY(-15px) scaleX(-1) scale(1.2) rotate(-10deg);
        filter: brightness(1.2) drop-shadow(0 0 20px rgba(255, 0, 85, 0.7));
    }
    80% {
        transform: translateX(5px) translateY(-5px) scaleX(-1) scale(1.05) rotate(5deg);
        filter: brightness(1.1) drop-shadow(0 0 10px rgba(255, 0, 85, 0.5));
    }
    100% {
        transform: translateX(0) translateY(0) scaleX(-1) scale(1) rotate(0deg);
        filter: brightness(1) drop-shadow(0 0 5px rgba(255, 0, 85, 0.3));
    }
}

.damage-animation .damage-effect {
    animation: damage 0.6s ease-out;
}

@keyframes damage {
    0% {
        opacity: 0;
        transform: scale(0.5);
    }
    30% {
        opacity: 1;
        transform: scale(1.2);
    }
    70% {
        opacity: 0.8;
        transform: scale(1.5);
    }
    100% {
        opacity: 0;
        transform: scale(2);
    }
}

@keyframes damage-pulse {
    0% {
        opacity: 0;
        transform: scale(0.8);
    }
    50% {
        opacity: 1;
        transform: scale(1.3);
    }
    100% {
        opacity: 0;
        transform: scale(1.8);
    }
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

@keyframes sparkle {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.2); }
}

/* Hide modals by default */
.results-modal {
    display: none;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .options-container {
        grid-template-columns: 1fr;
    }

    .robot {
        width: 100px;
        height: 150px;
    }

    .modal-content {
        padding: 20px;
        max-width: 90%;
    }

    .modal-content {
        padding: 30px 20px;
    }

    .modal h2 {
        font-size: 1.5rem;
    }

    #result-title {
        font-size: 2rem;
    }

    #instruction-title {
        font-size: 2rem;
    }

    .stars {
        font-size: 3rem;
        letter-spacing: 10px;
        margin: 20px 0;
    }

    #result-message {
        font-size: 1rem;
    }

    .instructions {
        padding: 20px;
    }

    .instructions p {
        font-size: 1.1rem;
    }

    .instructions li {
        font-size: 1rem;
        padding-left: 25px;
    }

    .exp-container {
        padding: 20px 30px;
        gap: 15px;
        margin: 20px 0;
    }

    .exp-icon {
        font-size: 2.5rem;
    }

    .exp-text {
        font-size: 2rem;
    }

    .modal-buttons {
        flex-direction: column;
        gap: 15px;
    }

    .start-btn, .replay-btn, .continue-btn, .try-again-btn, .exit-btn, .main-menu-btn {
        width: 100%;
        padding: 12px 20px;
        font-size: 1.1rem;
    }

    #start-btn {
        padding: 12px 30px;
        font-size: 1.1rem;
        min-width: 160px;
    }
}